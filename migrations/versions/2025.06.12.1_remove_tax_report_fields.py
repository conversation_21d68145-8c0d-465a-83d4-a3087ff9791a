"""remove tax report fields

Revision ID: 2025.06.12.1
Revises: 2025.06.12.0
Create Date: 2025-06-12 17:30:12.716631

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.06.12.1'
down_revision: Union[str, None] = '2025.06.12.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tax_reports', sa.Column('note', sa.Text(), nullable=True))
    op.drop_column('tax_reports', 'progress_steps')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tax_reports', sa.Column('progress_steps', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.drop_column('tax_reports', 'note')
    # ### end Alembic commands ###
