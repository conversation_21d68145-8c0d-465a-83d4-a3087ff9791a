"""client tasks new

Revision ID: 2025.06.11.1
Revises: 2025.06.11.0
Create Date: 2025-06-11 23:13:12.538107

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.06.11.1'
down_revision: Union[str, None] = '2025.06.11.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('tasks')
    op.add_column('client_tasks', sa.Column('client_service_id', sa.Integer(), nullable=True))
    op.add_column('client_tasks', sa.Column('tax_report_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'client_tasks', 'client_services', ['client_service_id'], ['id'])
    op.create_foreign_key(None, 'client_tasks', 'tax_reports', ['tax_report_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'client_tasks', type_='foreignkey')
    op.drop_constraint(None, 'client_tasks', type_='foreignkey')
    op.drop_column('client_tasks', 'tax_report_id')
    op.drop_column('client_tasks', 'client_service_id')
    op.create_table('tasks',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('client_service_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('tax_report_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('title', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('due_date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('assignee_manager_id', sa.VARCHAR(length=36), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('is_burning', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('notification_triggered_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('notification_dismissed_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['assignee_manager_id'], ['managers.id'], name='tasks_assignee_manager_id_fkey'),
    sa.ForeignKeyConstraint(['client_service_id'], ['client_services.id'], name='tasks_client_service_id_fkey'),
    sa.ForeignKeyConstraint(['tax_report_id'], ['tax_reports.id'], name='tasks_tax_report_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='tasks_pkey')
    )
    # ### end Alembic commands ###
