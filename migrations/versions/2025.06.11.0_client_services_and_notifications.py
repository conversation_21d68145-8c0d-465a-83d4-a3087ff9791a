"""client services and notifications

Revision ID: 2025.06.11.0
Revises: 2025.06.10.0
Create Date: 2025-06-11 22:57:46.793158

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2025.06.11.0'
down_revision: Union[str, None] = '2025.06.10.0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('notification_rules',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('object_type', sa.String(), nullable=False),
    sa.Column('object_id', sa.Integer(), nullable=True),
    sa.Column('offset_days', sa.Integer(), nullable=False),
    sa.Column('frequency', sa.String(), nullable=False),
    sa.Column('repeat_until_due', sa.Boolean(), nullable=True),
    sa.Column('channels', postgresql.JSON(astext_type=sa.Text()), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('schedule_config', postgresql.JSON(astext_type=sa.Text()), nullable=True),
    sa.Column('conditions', postgresql.JSON(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('notifications',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('rule_id', sa.Integer(), nullable=False),
    sa.Column('target_user_id', sa.BigInteger(), nullable=False),
    sa.Column('object_type', sa.String(), nullable=False),
    sa.Column('object_id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('message', sa.Text(), nullable=True),
    sa.Column('channel', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('sent_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('dismissed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('dismissed_by_user_id', sa.BigInteger(), nullable=True),
    sa.Column('next_notification_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('delivery_details', postgresql.JSON(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['rule_id'], ['notification_rules.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('service_components',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('service_id', sa.String(length=36), nullable=False),
    sa.Column('sequence', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['service_id'], ['services.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('client_service_components',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_service_id', sa.Integer(), nullable=False),
    sa.Column('service_id', sa.String(length=36), nullable=False),
    sa.Column('sequence', sa.Integer(), nullable=False),
    sa.Column('custom_price', sa.String(length=12), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['client_service_id'], ['client_services.id'], ),
    sa.ForeignKeyConstraint(['service_id'], ['services.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('tax_reports',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('fiscal_year', sa.String(), nullable=False),
    sa.Column('assignee_manager_id', sa.String(length=36), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('progress_steps', postgresql.JSON(astext_type=sa.Text()), nullable=True),
    sa.Column('file_id', sa.String(length=36), nullable=True),
    sa.Column('file_name', sa.String(), nullable=True),
    sa.Column('is_burning', sa.Boolean(), nullable=True),
    sa.Column('notification_triggered_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('notification_dismissed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('due_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['assignee_manager_id'], ['managers.id'], ),
    sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
    sa.ForeignKeyConstraint(['file_id'], ['files.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('tasks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_service_id', sa.Integer(), nullable=True),
    sa.Column('tax_report_id', sa.Integer(), nullable=True),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('due_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('assignee_manager_id', sa.String(length=36), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('is_burning', sa.Boolean(), nullable=True),
    sa.Column('notification_triggered_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('notification_dismissed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['assignee_manager_id'], ['managers.id'], ),
    sa.ForeignKeyConstraint(['client_service_id'], ['client_services.id'], ),
    sa.ForeignKeyConstraint(['tax_report_id'], ['tax_reports.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('client_tasks', sa.Column('description', sa.Text(), nullable=True))
    op.add_column('client_tasks', sa.Column('is_burning', sa.Boolean(), nullable=True))
    op.add_column('client_tasks', sa.Column('notification_triggered_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('client_tasks', sa.Column('notification_dismissed_at', sa.DateTime(timezone=True), nullable=True))
    op.alter_column('client_tasks', 'task',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.create_foreign_key(None, 'client_tasks', 'managers', ['manager_id'], ['id'])
    op.add_column('services', sa.Column('description', sa.Text(), nullable=True))
    op.add_column('services', sa.Column('composite_flag', sa.Boolean(), nullable=True))
    op.add_column('services', sa.Column('created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('services', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    op.alter_column('services', 'title',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('services', 'title',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.drop_column('services', 'updated_at')
    op.drop_column('services', 'created_at')
    op.drop_column('services', 'composite_flag')
    op.drop_column('services', 'description')
    op.drop_constraint(None, 'client_tasks', type_='foreignkey')
    op.alter_column('client_tasks', 'task',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.drop_column('client_tasks', 'notification_dismissed_at')
    op.drop_column('client_tasks', 'notification_triggered_at')
    op.drop_column('client_tasks', 'is_burning')
    op.drop_column('client_tasks', 'description')
    op.drop_table('tasks')
    op.drop_table('tax_reports')
    op.drop_table('client_service_components')
    op.drop_table('service_components')
    op.drop_table('notifications')
    op.drop_table('notification_rules')
    # ### end Alembic commands ###
