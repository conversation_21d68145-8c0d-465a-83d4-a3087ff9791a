import json
import os
from datetime import datetime

from sqlalchemy import (
    create_engine, update
)
from sqlalchemy.orm import sessionmaker

from records.api import auth_utils
from records.db import base, models


# Helper function to normalize the data
def normalize_json(obj):
    if isinstance(obj, dict):
        if '$oid' in obj:
            return obj['$oid']
        elif '$numberInt' in obj:
            return int(obj['$numberInt'])
        elif '$numberLong' in obj:
            return int(obj['$numberLong'])
        elif '$numberDouble' in obj:
            return float(obj['$numberDouble'])
        elif '$date' in obj:
            # Handle ISO date strings
            date_str = obj['$date']
            for fmt in ('%Y-%m-%dT%H:%M:%S.%fZ', '%Y-%m-%dT%H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S'):
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            return date_str  # Return as is if it doesn't match any format
        else:
            return {k: normalize_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [normalize_json(item) for item in obj]
    else:
        return obj


def migrate_client_services(session, client_data):
    client_id = client_data['uid']
    client = session.query(models.Client).filter_by(id=client_id).first()
    updated = 0
    if not client:
        return updated

    services = client_data.get('services', [])
    if not services:
        return updated

    for svc in services:
        service_title = svc.get('service')
        if isinstance(service_title, dict):
            service_title = service_title.get('title')

        discount = svc.get('discount', 0)
        price = svc.get('price', 0)
        total = svc.get('total', price)
        active_since = svc.get('active_since')

        service_obj = session.query(models.Service).filter_by(title=service_title).first()
        if not service_obj:
            print(f"Service '{service_title}' not found")
            continue

        # Check if the client-service relationship already exists
        existing_relationship = session.query(models.ClientService).filter_by(
            client_id=client_id, service_id=service_obj.id
        ).first()
        if existing_relationship:
            # Update and enrich fields
            update_q = update(models.ClientService).where(models.ClientService.id == existing_relationship.id)
            update_q = update_q.values(
                active_since=active_since,
                active_until=None,
                note=svc.get('note'),
                discount_percent=discount,
                total=str(total),
            )
            session.execute(update_q)
        else:
            # Create new relationship
            client_service = models.ClientService(
                client_id=client_id,
                service_id=service_obj.id,
                active_since=active_since,
                active_until=None,
                note=svc.get('note'),
                discount_percent=discount,
                total=str(total),
            )
            session.add(client_service)
        updated += 1

    return updated


def process_clients(session, filename):
    with open(filename, 'r') as f:
        updated = 0
        for line in f:
            data = json.loads(line, object_hook=normalize_json)
            client_data = data.copy()
            updated_tmp = migrate_client_services(session, client_data)
            if updated_tmp:
                updated += updated_tmp

        print(f"Processed {filename}, updated {updated} records")


def main():
    engine = create_engine(
        f'postgresql://{os.getenv("PGUSER")}:{os.getenv("PGPASSWORD")}@{os.getenv("PGHOST", "localhost")}:5432/{os.getenv("PGDATABASE")}',
        echo=os.getenv("DEBUG_DB", "false") == "true",
    )
    session = sessionmaker(bind=engine)()
    base.ModelBase.metadata.create_all(engine)

    try:
        process_clients(session, 'clients.jsonl')
        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    # Close the session
    session.close()


if __name__ == '__main__':
    main()
